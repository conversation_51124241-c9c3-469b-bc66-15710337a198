/**
 * OrganizeTaskHandler
 * 负责整理论文相关的任务处理（占位版带通知）
 * - 只接受“单论文”的 ORGANIZE_PAPERS 任务（papers.length 必须为 1）
 * - 暂不实现具体业务，仅构造一个成功的占位结果
 * - 执行完成后通过 PaperOrganizationService 进行结果通知
 */

import { BaseHandler } from '../baseHandler.js';
import { PERSISTENCE_STRATEGY, ORGANIZE_SUPPORTED_TASK_TYPES } from '../../../constants.js';
import { logger } from '../../../util/logger.js';
import { paperOrganizationService } from '../../feature/paperOrganizationService.js';
import { fileManagementService } from '../../../service/fileManagementService.js';
import aiServiceInstance from '../../../service/aiService.js';

export class OrganizeTaskHandler extends BaseHandler {
  constructor() {
    const config = {
      maxConcurrency: 1,
      queueConfig: {
        executionQueueSize: 3,
        waitingQueueSize: 10
      },
      persistenceConfig: {
        strategy: PERSISTENCE_STRATEGY.NONE
      }
    };
    super('OrganizeTaskHandler', config);
  }

  /**
   * 返回支持的任务类型
   */
  getSupportedTaskTypes() {
    return Object.values(ORGANIZE_SUPPORTED_TASK_TYPES);
  }

  /**
   * 执行任务入口
   */
  async execute(task) {
    logger.log(`[${this.handlerName}] 收到整理任务: ${task.key}`);
    switch (task.type) {
      case ORGANIZE_SUPPORTED_TASK_TYPES.ORGANIZE_PAPER:
        return await this.executeOrganizePaper(task);
      default:
        return { success: false, error: `不支持的任务类型: ${task.type}` };
    }
  }

  /**
   * 整理论文执行逻辑
   * 处理PDF下载、翻译和分类功能
   */
  async executeOrganizePaper(task) {
    logger.log("执行整理论文任务", task);
    const papers = task?.params?.papers || [];
    const options = task?.params?.options || {};

    logger.log("整理选项详情:", {
      downloadPdf: options.downloadPdf,
      translation: options.translation,
      classification: options.classification,
      storage: options.storage,
      selectedPapers: options.selectedPapers,
      totalPapers: options.totalPapers,
      timestamp: options.timestamp
    });

    const results = [];

    for (const paper of papers) {
      try {
        const result = await this._processSinglePaper(paper, options);
        results.push({ id: paper.id, success: true, result });
      } catch (error) {
        logger.error(`处理论文 ${paper.id} 失败:`, error);
        results.push({ id: paper.id, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failedCount = results.length - successCount;

    return {
      success: true,
      data: {
        processed: results.length,
        success: successCount,
        failed: failedCount,
        details: results
      }
    };
  }

  /**
   * 处理单篇论文
   * @param {Object} paper 论文对象
   * @param {Object} options 处理选项
   * @returns {Promise<Object>} 处理结果
   */
  async _processSinglePaper(paper, options) {
    const result = {
      paperId: paper.id,
      title: paper.title,
      actions: [],
      storage: options.storage || {}
    };

    // 0. 存储路径设置和目录创建
    if (options.storage?.taskDirectory) {
      try {
        logger.log(`[${paper.id}] 设置存储路径: ${options.storage.taskDirectory}`);

        // 使用文件管理服务创建子目录
        const dirResult = await fileManagementService.createSubDirectory(options.storage.taskDirectory);

        if (dirResult.success) {
          result.actions.push({
            type: 'storage',
            status: 'completed',
            workingDirectory: fileManagementService.getWorkingDirectoryName(),
            taskDirectory: dirResult.taskDirectory,
            fullPath: dirResult.fullPath,
            message: dirResult.message
          });

          // 更新result中的存储信息
          result.storage = {
            workingDirectory: fileManagementService.getWorkingDirectoryName(),
            taskDirectory: dirResult.taskDirectory,
            fullPath: dirResult.fullPath
          };
        } else {
          throw new Error(dirResult.error || '目录创建失败');
        }
      } catch (error) {
        logger.error(`[${paper.id}] 存储路径设置失败:`, error);
        result.actions.push({
          type: 'storage',
          status: 'failed',
          error: error.message,
          message: `存储路径设置失败: ${error.message}`
        });
      }
    }
    let translatedAbstract = paper.abstract;
    // 2. 翻译功能
    if (options.translation?.enabled) {
      logger.log(`[${paper.id}] 执行翻译到 ${options.translation.targetLanguage}`);
      translatedAbstract = await aiServiceInstance.translateAbstract(paper.abstract, options.translation.targetLanguage);
    }

    // 3. 分类功能
    if (options.classification?.enabled) {
      logger.log(`[${paper.id}] 执行分类，标准: ${options.classification.selectedStandard}`);
      result.actions.push({
        type: 'classification',
        status: 'completed',
        standard: options.classification.selectedStandard,
        message: '论文分类已完成'
      });
    }

    // 4. 生成CSV文件
    if (options.storage?.taskDirectory) {
      try {
        logger.log(`[${paper.id}] 生成CSV文件`);

        const csvResult = await this._savePaperToCsv(paper, translatedAbstract, options);

        if (csvResult.success) {
          result.actions.push({
            type: 'csv_export',
            status: 'completed',
            filename: csvResult.filename,
            downloadId: csvResult.downloadId,
            fullPath: csvResult.fullPath,
            message: 'CSV文件已生成并保存'
          });
        } else {
          result.actions.push({
            type: 'csv_export',
            status: 'failed',
            error: csvResult.error,
            message: `CSV文件生成失败: ${csvResult.error}`
          });
        }
      } catch (error) {
        logger.error(`[${paper.id}] CSV文件生成失败:`, error);
        result.actions.push({
          type: 'csv_export',
          status: 'failed',
          error: error.message,
          message: `CSV文件生成失败: ${error.message}`
        });
      }
    }

    return result;
  }

  /**
   * 保存论文信息到CSV文件
   * @param {Object} paper - 论文对象
   * @param {string} translatedAbstract - 翻译后的摘要
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 保存结果
   * @private
   */
  async _savePaperToCsv(paper, translatedAbstract, options) {
    try {
      // 准备CSV数据
      const csvData = this._prepareCsvData(paper, translatedAbstract, options);

      // 生成文件名（基于论文标题）
      const baseFilename = paper.title || 'paper';

      // 使用fileManagementService保存CSV
      const result = await fileManagementService.saveCsvFile(
        csvData,
        baseFilename,
        options.storage.taskDirectory
      );

      if (result.success) {
        logger.log(`[OrganizeTaskHandler] CSV文件已保存: ${result.fullPath}`);
      }

      return result;
    } catch (error) {
      logger.error('[OrganizeTaskHandler] CSV文件保存失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 准备CSV数据格式
   * @param {Object} paper - 论文对象
   * @param {string} translatedAbstract - 翻译后的摘要
   * @param {Object} options - 选项
   * @returns {Object} CSV数据对象，包含headers和rows
   * @private
   */
  _prepareCsvData(paper, translatedAbstract, options) {
    // CSV头部
    const headers = [
      'ID',
      'Title',
      'Authors',
      'Original Abstract',
      'Translated Abstract',
      'Target Language',
      'All Versions URL',
      'Export Date'
    ];

    // 数据行
    const row = [
      paper.id || '',
      paper.title || '',
      paper.authors || '',
      paper.abstract || '',
      translatedAbstract || '',
      options.translation?.targetLanguage || '',
      paper.allVersionUrl || '',
      new Date().toISOString()
    ];

    return {
      headers: headers,
      rows: [row]
    };
  }

  /**
   * 可选校验钩子（占位）
   */
  validateSpecificTask(task) {
    return true;
  }

  async beforeExecute(task) {
    await super.beforeExecute(task);
  }

  /**
   * 执行完成后通知 PaperOrganizationService（若其维护了该 taskKey 的索引则会更新）
   */
  async afterExecute(task, result) {
    try {
      await super.afterExecute(task, result);
    } finally {
      // 将占位结果回传给组织服务，用于更新对应批次/论文的状态
      try {
        const notifyOk = paperOrganizationService.notifyOrganizeTaskCompleted(task.key, {
          success: !!result?.success,
          error: result?.error
        });
        if (!notifyOk) {
          logger.debug(`[${this.handlerName}] 未找到对应批次映射（可能来自旧入口或多论文任务）: ${task.key}`);
        }
      } catch (e) {
        logger.warn(`[${this.handlerName}] 通知组织服务失败: ${e?.message}`);
      }
    }
  }
}

export default OrganizeTaskHandler;
