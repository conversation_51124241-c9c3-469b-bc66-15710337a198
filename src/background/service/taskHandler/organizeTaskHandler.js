/**
 * OrganizeTaskHandler
 * 负责整理论文相关的任务处理（占位版带通知）
 * - 只接受“单论文”的 ORGANIZE_PAPERS 任务（papers.length 必须为 1）
 * - 暂不实现具体业务，仅构造一个成功的占位结果
 * - 执行完成后通过 PaperOrganizationService 进行结果通知
 */

import { BaseHandler } from '../baseHandler.js';
import { PERSISTENCE_STRATEGY, ORGANIZE_SUPPORTED_TASK_TYPES } from '../../../constants.js';
import { logger } from '../../../util/logger.js';
import { paperOrganizationService } from '../../feature/paperOrganizationService.js';
import { fileManagementService } from '../../../service/fileManagementService.js';
import aiServiceInstance from '../../../service/aiService.js';

export class OrganizeTaskHandler extends BaseHandler {
  constructor() {
    const config = {
      maxConcurrency: 1,
      queueConfig: {
        executionQueueSize: 3,
        waitingQueueSize: 10
      },
      persistenceConfig: {
        strategy: PERSISTENCE_STRATEGY.NONE
      }
    };
    super('OrganizeTaskHandler', config);
  }

  /**
   * 返回支持的任务类型
   */
  getSupportedTaskTypes() {
    return Object.values(ORGANIZE_SUPPORTED_TASK_TYPES);
  }

  /**
   * 执行任务入口
   */
  async execute(task) {
    logger.log(`[${this.handlerName}] 收到整理任务: ${task.key}`);
    switch (task.type) {
      case ORGANIZE_SUPPORTED_TASK_TYPES.ORGANIZE_PAPER:
        return await this.executeOrganizePaper(task);
      default:
        return { success: false, error: `不支持的任务类型: ${task.type}` };
    }
  }

  /**
   * 整理论文执行逻辑
   * 处理PDF下载、翻译和分类功能
   */
  async executeOrganizePaper(task) {
    logger.log("执行整理论文任务", task);
    const papers = task?.params?.papers || [];
    const options = task?.params?.options || {};

    logger.log("整理选项详情:", {
      downloadPdf: options.downloadPdf,
      translation: options.translation,
      classification: options.classification,
      storage: options.storage,
      selectedPapers: options.selectedPapers,
      totalPapers: options.totalPapers,
      timestamp: options.timestamp
    });

    const results = [];

    for (const paper of papers) {
      try {
        const result = await this._processSinglePaper(paper, options);
        results.push({ id: paper.id, success: true, result });
      } catch (error) {
        logger.error(`处理论文 ${paper.id} 失败:`, error);
        results.push({ id: paper.id, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failedCount = results.length - successCount;

    return {
      success: true,
      data: {
        processed: results.length,
        success: successCount,
        failed: failedCount,
        details: results
      }
    };
  }

  /**
   * 处理单篇论文
   * @param {Object} paper 论文对象
   * @param {Object} options 处理选项
   * @returns {Promise<Object>} 处理结果
   */
  async _processSinglePaper(paper, options) {
    const result = {
      paperId: paper.id,
      title: paper.title,
      actions: [],
      storage: options.storage || {}
    };

    // 1. 设置存储路径
    await this._handleStorage(paper, options, result);

    // 2. 执行翻译
    const translatedAbstract = await this._handleTranslation(paper, options, result);

    // 3. 执行分类（如果启用）
    await this._handleClassification(paper, options, result);

    // 4. 生成CSV文件（如果有存储路径）
    await this._handleCsvExport(paper, translatedAbstract, options, result);

    return result;
  }

  /**
   * 处理存储路径设置
   * @param {Object} paper - 论文对象
   * @param {Object} options - 选项
   * @param {Object} result - 结果对象
   * @private
   */
  async _handleStorage(paper, options, result) {
    if (!options.storage?.taskDirectory) return;

    await this._executeAction('storage', paper.id, async () => {
      logger.log(`[${paper.id}] 设置存储路径: ${options.storage.taskDirectory}`);

      const dirResult = await fileManagementService.createSubDirectory(options.storage.taskDirectory);

      if (!dirResult.success) {
        throw new Error(dirResult.error || '目录创建失败');
      }

      // 更新result中的存储信息
      result.storage = {
        workingDirectory: fileManagementService.getWorkingDirectoryName(),
        taskDirectory: dirResult.taskDirectory,
        fullPath: dirResult.fullPath
      };

      return {
        workingDirectory: fileManagementService.getWorkingDirectoryName(),
        taskDirectory: dirResult.taskDirectory,
        fullPath: dirResult.fullPath,
        message: dirResult.message
      };
    }, result);
  }

  /**
   * 处理翻译功能
   * @param {Object} paper - 论文对象
   * @param {Object} options - 选项
   * @param {Object} result - 结果对象
   * @returns {Promise<string>} 翻译后的摘要
   * @private
   */
  async _handleTranslation(paper, options, result) {
    if (!options.translation?.enabled) {
      return paper.abstract;
    }

    return await this._executeAction('translation', paper.id, async () => {
      logger.log(`[${paper.id}] 执行翻译到 ${options.translation.targetLanguage}`);

      const translatedText = await aiServiceInstance.translateAbstract(
        paper.abstract,
        options.translation.targetLanguage
      );

      if (!translatedText) {
        throw new Error('翻译失败，返回空结果');
      }

      return {
        originalText: paper.abstract,
        translatedText: translatedText,
        targetLanguage: options.translation.targetLanguage,
        message: `翻译到${options.translation.targetLanguage}已完成`
      };
    }, result, paper.abstract); // 失败时返回原文
  }

  /**
   * 处理分类功能
   * @param {Object} paper - 论文对象
   * @param {Object} options - 选项
   * @param {Object} result - 结果对象
   * @private
   */
  async _handleClassification(paper, options, result) {
    if (!options.classification?.enabled) return;

    // 目前是占位实现
    await this._executeAction('classification', paper.id, async () => {
      logger.log(`[${paper.id}] 执行分类，标准: ${options.classification.selectedStandard}`);

      // TODO: 实际的分类逻辑
      return {
        standard: options.classification.selectedStandard,
        message: '论文分类已完成'
      };
    }, result);
  }

  /**
   * 处理CSV导出功能
   * @param {Object} paper - 论文对象
   * @param {string} translatedAbstract - 翻译后的摘要
   * @param {Object} options - 选项
   * @param {Object} result - 结果对象
   * @private
   */
  async _handleCsvExport(paper, translatedAbstract, options, result) {
    if (!options.storage?.taskDirectory) return;

    await this._executeAction('csv_export', paper.id, async () => {
      logger.log(`[${paper.id}] 生成CSV文件`);

      const csvData = this._prepareCsvData(paper, translatedAbstract, options);
      const baseFilename = paper.title || 'paper';

      const csvResult = await fileManagementService.saveCsvFile(
        csvData,
        baseFilename,
        options.storage.taskDirectory
      );

      if (!csvResult.success) {
        throw new Error(csvResult.error || 'CSV保存失败');
      }

      return {
        filename: csvResult.filename,
        downloadId: csvResult.downloadId,
        fullPath: csvResult.fullPath,
        message: 'CSV文件已生成并保存'
      };
    }, result);
  }

  /**
   * 通用的动作执行器，统一处理错误和结果记录
   * @param {string} actionType - 动作类型
   * @param {string} paperId - 论文ID
   * @param {Function} actionFn - 执行的异步函数
   * @param {Object} result - 结果对象
   * @param {any} fallbackValue - 失败时的回退值
   * @returns {Promise<any>} 执行结果或回退值
   * @private
   */
  async _executeAction(actionType, paperId, actionFn, result, fallbackValue = null) {
    try {
      const actionResult = await actionFn();

      result.actions.push({
        type: actionType,
        status: 'completed',
        ...actionResult
      });

      return actionResult.translatedText || actionResult;
    } catch (error) {
      logger.error(`[${paperId}] ${actionType}失败:`, error);

      result.actions.push({
        type: actionType,
        status: 'failed',
        error: error.message,
        message: `${actionType}失败: ${error.message}`
      });

      return fallbackValue;
    }
  }

  /**
   * 准备CSV数据格式
   * @param {Object} paper - 论文对象
   * @param {string} translatedAbstract - 翻译后的摘要
   * @param {Object} options - 选项
   * @returns {Object} CSV数据对象，包含headers和rows
   * @private
   */
  _prepareCsvData(paper, translatedAbstract, options) {
    const headers = [
      'ID', 'Title', 'Authors', 'Original Abstract',
      'Translated Abstract', 'Target Language', 'All Versions URL', 'Export Date'
    ];

    const row = [
      paper.id || '', paper.title || '', paper.authors || '', paper.abstract || '',
      translatedAbstract || '', options.translation?.targetLanguage || '',
      paper.allVersionUrl || '', new Date().toISOString()
    ];

    return { headers, rows: [row] };
  }

  /**
   * 可选校验钩子（占位）
   */
  validateSpecificTask(task) {
    return true;
  }

  async beforeExecute(task) {
    await super.beforeExecute(task);
  }

  /**
   * 执行完成后通知 PaperOrganizationService（若其维护了该 taskKey 的索引则会更新）
   */
  async afterExecute(task, result) {
    try {
      await super.afterExecute(task, result);
    } finally {
      // 将占位结果回传给组织服务，用于更新对应批次/论文的状态
      try {
        const notifyOk = paperOrganizationService.notifyOrganizeTaskCompleted(task.key, {
          success: !!result?.success,
          error: result?.error
        });
        if (!notifyOk) {
          logger.debug(`[${this.handlerName}] 未找到对应批次映射（可能来自旧入口或多论文任务）: ${task.key}`);
        }
      } catch (e) {
        logger.warn(`[${this.handlerName}] 通知组织服务失败: ${e?.message}`);
      }
    }
  }
}

export default OrganizeTaskHandler;
